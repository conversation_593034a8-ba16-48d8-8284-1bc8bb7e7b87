<template>
  <div class="common-table-page">
    <!-- 筛选栏 -->
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <div class="flex row-pad">
        <a-form-item label="年份">
          <a-date-picker mode="year" format="YYYY" v-model="queryParam.year" placeholder="请选择" allow-clear
            :open="yearShowOne" style="width: 120px" @keyup.enter.native="handleQuery" @openChange="openChangeOne"
            @panelChange="panelChangeOne"></a-date-picker>
        </a-form-item>
        <a-form-item label="单位" style="margin-left: 20px">
          <a-select style="width: 130px" v-model="queryParam.depId" placeholder="请选择" :disabled="loginOrgId != 10020"
            @change="changeDept">
            <a-select-option v-for="(d, index) in deptOptions" :key="index" :value="d.deptId">
              {{ d.deptName }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </div>

      <template #tab>
        <div class="row-pad"
          style="padding-top: 15px; display: flex; justify-content: space-between; align-items: center">
          <a-radio-group default-value="1" v-model="queryParam.irrigationType" button-style="solid"
            @change="onClickChannel">
            <a-radio-button value="1">按渠道分 表1</a-radio-button>
            <a-radio-button value="2">按渠道分 表2</a-radio-button>
            <a-radio-button value="3">按渠道分 表3</a-radio-button>
            <a-radio-button value="4">按行政区划分 表4</a-radio-button>
          </a-radio-group>
          <div class="table-operations">
            <a-button type="primary" @click="handleImport" icon="import" :loading="importLoading">导入</a-button>
            <a-button type="primary" @click="handleExport" icon="export" :loading="exportLoading">导出</a-button>
          </div>
        </div>
      </template>

      <template #table>
        <div class="table-container">
          <!-- 表格标题 -->
          <div class="table-title" v-if="tableTitle">
            <h3>{{ tableTitle }}</h3>
            <div class="unit-label">单位：万亩</div>
          </div>

          <vxe-table ref="vxeTableRef" :isAdaptPageSize="false" :data="displayTableData" border stripe :row-config="{ keyField: 'id' }"
            :column-config="{ resizable: true }" class="plant-intention-table"
            :empty-text="'暂无数据'" :loading="loading" :merge-cells="mergeCells">
            <!-- 第一列：类型 -->
            <vxe-column field="type" title="类型" width="100" fixed="left" align="center" header-align="center">
              <template #default="{ row }">
                <span :class="row.isTotal ? 'total-cell' : 'type-cell'">{{ row.type }}</span>
              </template>
            </vxe-column>

            <!-- 第二列：渠道/行政区划 -->
            <vxe-column field="name" :title="firstColumnTitle" width="120" fixed="left" align="center"
              header-align="center">
              <template #default="{ row }">
                <span :class="row.isTotal ? 'total-cell' : ''">{{ row.name }}</span>
              </template>
            </vxe-column>

            <!-- 动态渲染表头 -->
            <template v-for="column in tableColumns">
              <!-- 分组列 -->
              <vxe-colgroup v-if="column.type === 'group'" :title="column.title" align="center" header-align="center">
                <template v-for="subColumn in column.children">
                  <!-- 子分组列 -->
                  <vxe-colgroup v-if="subColumn.type === 'group'" :title="subColumn.title" align="center"
                    header-align="center">
                    <template v-for="subSubColumn in subColumn.children">
                      <vxe-column v-if="subSubColumn.field" :field="subSubColumn.field" :title="subSubColumn.title"
                        :width="subSubColumn.width" align="center" header-align="center">
                        <template #default="{ row }">
                          <span v-if="row.isTotal" class="total-cell">
                            {{ formatNumber(row[subSubColumn.field]) }}
                          </span>
                          <span v-else-if="subSubColumn.isSubtotal" :style="{ fontWeight: 'bold', color: '#1890ff' }">
                            {{ formatNumber(row[subSubColumn.field]) }}
                          </span>
                          <span v-else>
                            {{ formatNumber(row[subSubColumn.field]) }}
                          </span>
                        </template>
                      </vxe-column>
                    </template>
                  </vxe-colgroup>
                  <!-- 普通子列 -->
                  <vxe-column v-else-if="subColumn.field" :field="subColumn.field" :title="subColumn.title"
                    :width="subColumn.width" align="center" header-align="center">
                    <template #default="{ row }">
                      <span v-if="row.isTotal" class="total-cell">
                        {{ formatNumber(row[subColumn.field]) }}
                      </span>
                      <span v-else-if="subColumn.isSubtotal" :style="{ fontWeight: 'bold', color: '#52c41a' }">
                        {{ formatNumber(row[subColumn.field]) }}
                      </span>
                      <span v-else>
                        {{ formatNumber(row[subColumn.field]) }}
                      </span>
                    </template>
                  </vxe-column>
                </template>
              </vxe-colgroup>
              <!-- 普通列 -->
              <vxe-column v-else :field="column.field" :title="column.title" :width="column.width" align="center"
                header-align="center">
                <template #default="{ row }">
                  <span v-if="row.isTotal" class="total-cell">
                    {{ formatNumber(row[column.field]) }}
                  </span>
                  <span v-else-if="column.isTotal" :style="{ fontWeight: 'bold', color: '#52c41a' }">
                    {{ formatNumber(row[column.field]) }}
                  </span>
                  <span v-else>
                    {{ formatNumber(row[column.field]) }}
                  </span>
                </template>
              </vxe-column>
            </template>
          </vxe-table>
        </div>
      </template>
    </VxeTableForm>
  </div>
</template>

<script>
import VxeTableForm from '@/components/VxeTableForm'
import { getValueByKey, getTreeByLoginOrgId } from '@/api/common'
import {
  plantingCrop,
  getValueToFieldNameMap,
  findCropByPlantTypeCode,
  findCropByTypeCodes,
  testCropMatching,
  getLeafCropsWithFieldNames,
} from '@/enum/planting-crop'
import { getPlantIntention, getDeptList } from './services'
import { getProjectPage } from '@/api/common'
import { getOptions } from '@/api/common'
import { getDistrictPage } from '@/views/basic/district/services'
import moment from 'moment'

export default {
  name: 'PlantIntention',
  components: {
    VxeTableForm,
  },
  data() {
    return {
      rangeDate: [],
      list: [],
      loading: false,
      yearShowOne: false,
      tableTitle: '',

      deptOptions: [],
      exportLoading: false,
      importLoading: false,
      loginOrgId: JSON.parse(localStorage.getItem('user'))?.loginOrgId,
      queryParam: {
        depId: undefined,
        irrigationType: '1',
        planYear: moment().year(),
        shardType: 1,
        year: moment(), // 添加年份选择器的默认值
      },

      // 表格数据
      tableData: [],

      // 作物分类数据
      plantingCropCategories: [],

      // 表格列配置
      tableColumns: [],

      // 项目数据（用于渠道名称匹配）
      projectOptions: [],

      // 作物选项数据
      cropOptions: [],

      // 原始响应数据
      cstIrrigationDtoList: [],

      // 当前年份
      currentYear: moment().year(),

      // 行政区划数据（用于区划名称匹配）
      districtOptions: [],
    }
  },
  computed: {
    // 第一列标题
    firstColumnTitle() {
      return this.queryParam.irrigationType === '4' ? '行政区划' : '渠道'
    },

    // 当前类型名称
    currentTypeName() {
      const typeMap = {
        1: '引黄灌溉',
        2: '引黄滴灌',
        3: '纯井灌',
      }
      return typeMap[this.queryParam.irrigationType] || ''
    },

    // 显示的表格数据
    displayTableData() {
      if (this.tableData.length === 0) {
        return []
      }

      // 创建合计行
      const totalRow = {
        id: 'total_row',
        name: this.queryParam.irrigationType === '4' ? '' : '合计', // 第4个tab时name为空，因为会被合并
        type: this.queryParam.irrigationType === '4' ? '灌域合计' : this.currentTypeName,
        isTotal: true, // 标记为合计行
      }

      // 获取所有需要计算合计的字段
      const leafCrops = getLeafCropsWithFieldNames()
      const fields = leafCrops.map(crop => crop.fieldName).filter(Boolean)

      // 添加其他字段
      fields.push('farmlandArea', 'totalIrrigationArea')

      // 添加小计字段
      this.plantingCropCategories.forEach(category => {
        const subtotalField = this.getSubtotalFieldName(category)
        if (subtotalField) {
          fields.push(subtotalField)
        }

        if (category.children) {
          category.children.forEach(subCategory => {
            if (subCategory.children && subCategory.children.length > 0) {
              const subSubtotalField = this.getSubtotalFieldName(subCategory)
              if (subSubtotalField) {
                fields.push(subSubtotalField)
              }
            }
          })
        }
      })

      fields.forEach(field => {
        let total = 0
        this.tableData.forEach(row => {
          total += parseFloat(row[field]) || 0
        })
        totalRow[field] = total > 0 ? total.toFixed(2) : null
      })

      // 将合计行添加到数据末尾
      return [...this.tableData, totalRow]
    },

    // 合并单元格配置
    mergeCells() {
      const displayData = this.displayTableData
      if (displayData.length === 0) {
        return []
      }

      const mergeCells = []

      if (this.queryParam.irrigationType === '4') {
        // 按行政区划分时，需要按类型分组合并
        const typeGroups = {}
        displayData.forEach((row, index) => {
          if (!typeGroups[row.type]) {
            typeGroups[row.type] = []
          }
          typeGroups[row.type].push(index)
        })

        // 为每个类型创建合并单元格
        Object.values(typeGroups).forEach(indices => {
          if (indices.length > 1) {
            mergeCells.push({
              row: indices[0],
              col: 0,
              rowspan: indices.length,
              colspan: 1,
            })
          }
        })

        // 合计行的前两列合并为"灌域合计"
        const totalRowIndex = displayData.findIndex(row => row.isTotal)
        if (totalRowIndex !== -1) {
          mergeCells.push({
            row: totalRowIndex,
            col: 0,
            rowspan: 1,
            colspan: 2,
          })
        }
      } else {
        // 按渠道分类时，整列合并（包括合计行）
        mergeCells.push({
          row: 0,
          col: 0,
          rowspan: displayData.length,
          colspan: 1,
        })
      }

      return mergeCells
    },
  },

  async created() {
    this.initPlantingCropCategories()
    this.initTableColumns()

    // 并行加载基础数据
    await Promise.all([
      this.loadCropOptions(),
      this.loadProjectOptions()
    ])

    // 设置初始标题
    this.setTableTitle(this.queryParam.planYear)

    getTreeByLoginOrgId().then(treeRes => {
      this.deptOptions.push(treeRes?.data[0])
      getDeptList({ parentId: treeRes?.data[0].deptId }).then(res => {
        this.deptOptions = this.deptOptions.concat(res?.data)
        // 重新设置标题，因为现在有了单位信息
        this.setTableTitle(this.queryParam.planYear)
      })
      this.getList()
    })
  },

  methods: {
    changeDept() {
      // 切换单位时重新设置标题
      if (this.currentYear) {
        this.setTableTitle(this.currentYear)
      }
      this.getList()
    },

    onClickChannel(val) {
      // 切换分表类型
      const value = val.target ? val.target.value : val
      this.queryParam.shardType = value === '4' ? 2 : 1

      // 切换tab时重新设置标题
      if (this.currentYear) {
        this.setTableTitle(this.currentYear)
      }

      this.getList()
    },

    // 弹出日历和关闭日历的回调
    openChangeOne(status) {
      this.yearShowOne = status
    },

    // 得到年份选择器的值
    panelChangeOne(value) {
      this.queryParam.planYear = moment(value).year()
      this.queryParam.year = value // 同步更新年份选择器的值
      this.yearShowOne = false

      // 切换年份时重新设置标题
      this.setTableTitle(this.queryParam.planYear)

      this.handleQuery()
    },

    /** 查询列表 */
    async getList() {
      this.loading = true
      this.queryParam.depId = this.queryParam.depId || this.loginOrgId

      // 确保年份参数格式正确
      const requestParam = {
        ...this.queryParam,
        planYear: this.queryParam.year ? moment(this.queryParam.year).year() : this.queryParam.planYear,
      }

      try {
        // 确保项目数据已加载（用于渠道名称显示）
        if (this.projectOptions.length === 0) {
          await this.loadProjectOptions()
        }

        const response = await getPlantIntention(requestParam)

        if (response.code === 200 && response.data) {
          this.cstIrrigationDtoList = response.data.cstIrrigationDtoList || []
          this.currentYear = response.data.planYear

          // 设置表格标题
          this.setTableTitle(response.data.planYear)

          // 处理表格数据
          await this.processTableData()
        } else {
          this.$message.error(response.message || '获取数据失败')
          this.tableData = []
        }
      } catch (error) {
        console.error('获取列表数据失败:', error)
        this.$message.error('获取数据失败')
        this.tableData = []
      } finally {
        this.loading = false
      }
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryParam = {
        depId: this.loginOrgId,
        irrigationType: '1',
        planYear: moment().year(),
        shardType: 1,
        year: moment(), // 重置时也设置默认年份
      }
      this.handleQuery()
    },

    // 处理表格数据
    async processTableData() {
      this.tableData = []

      if (!this.cstIrrigationDtoList || this.cstIrrigationDtoList.length === 0) {
        return
      }

      // 根据分表类型处理数据
      if (this.queryParam.shardType === 2) {
        // 按行政区划分
        await this.processDistrictData()
      } else {
        // 按渠道分
        this.processChannelData()
      }
    },

    // 处理按渠道分的数据
    processChannelData() {
      const targetIrrigationType = parseInt(this.queryParam.irrigationType)
      const irrigationData = this.cstIrrigationDtoList.find(item => item.irrigationType === targetIrrigationType)

      if (!irrigationData || !irrigationData.channelDtoList) {
        return
      }

      irrigationData.channelDtoList.forEach((channelDto, index) => {
        const { channelNo, cstIrrigationPlantIntentDetailDtoList } = channelDto

        // 获取渠道名称
        const channelName = this.getChannelName(channelNo)

        // 创建行数据
        const rowData = {
          id: `${channelNo}_${index}`,
          type: this.currentTypeName,
          name: channelName,
          channelCode: channelNo,
          farmlandArea: null,
          totalIrrigationArea: null,
        }

        // 初始化所有作物字段
        this.initializeCropFields(rowData)

        // 填充种植数据
        this.fillPlantingData(rowData, cstIrrigationPlantIntentDetailDtoList || [])

        // 计算小计和总计
        this.calculateSubtotals(rowData)

        this.tableData.push(rowData)
      })
    },

    // 处理按行政区划分的数据
    async processDistrictData() {
      // 按行政区划分时，需要处理所有灌溉类型的数据，但过滤掉引黄滴灌(irrigationType=2)
      for (const irrigationData of this.cstIrrigationDtoList) {
        if (!irrigationData.districtDtoList) {
          continue
        }

        // 过滤掉引黄滴灌的数据(irrigationType=2)
        if (irrigationData.irrigationType === 2) {
          continue
        }

        const typeName = this.getTypeNameByIrrigationType(irrigationData.irrigationType)

        for (const districtDto of irrigationData.districtDtoList) {
          const { district, cstIrrigationPlantIntentDetailDtoList } = districtDto

          // 获取行政区划名称
          const districtName = await this.getDistrictName(district)

          // 创建行数据
          const rowData = {
            id: `${district}_${irrigationData.irrigationType}`,
            type: typeName,
            name: districtName,
            districtCode: district,
            farmlandArea: null,
            totalIrrigationArea: null,
          }

          // 初始化所有作物字段
          this.initializeCropFields(rowData)

          // 填充种植数据
          this.fillPlantingData(rowData, cstIrrigationPlantIntentDetailDtoList || [])

          // 计算小计和总计
          this.calculateSubtotals(rowData)

          this.tableData.push(rowData)
        }
      }
    },

    // 初始化作物字段
    initializeCropFields(rowData) {
      const leafCrops = getLeafCropsWithFieldNames()
      leafCrops.forEach(crop => {
        if (crop.fieldName) {
          rowData[crop.fieldName] = null
        }
      })

      // 初始化小计字段
      const subtotalFields = [
        'melonVegetableSubtotal',
        'wheatIntercroppingSubtotal',
        'summerCropsSubtotal',
        'autumnCropsSubtotal',
        'forestlandSubtotal',
        'forestGrasslandSubtotal',
      ]
      subtotalFields.forEach(field => {
        rowData[field] = null
      })
    },

    // 填充种植数据
    fillPlantingData(rowData, plantIntentDetailList) {
      plantIntentDetailList.forEach(detail => {
        const { plantTypeCode, plantAmount, firstTypeCode, secondTypeCode } = detail

        // 使用精确匹配方法，同时考虑firstTypeCode和secondTypeCode
        const cropInfo = findCropByTypeCodes(plantTypeCode, firstTypeCode, secondTypeCode, this.cropOptions)

        if (cropInfo && cropInfo.fieldName && plantAmount > 0) {
          rowData[cropInfo.fieldName] = plantAmount
        }
      })
    },

    // 计算小计和总计
    calculateSubtotals(rowData) {
      // 计算瓜菜小计
      const melonVegetableFields = ['melon', 'sunflowerInMelon', 'tomato']
      rowData.melonVegetableSubtotal = this.calculateFieldsSum(rowData, melonVegetableFields)

      // 计算小麦间套种小计
      const wheatIntercroppingFields = ['corn', 'pepper', 'sunflowerInWheat', 'otherInWheat']
      rowData.wheatIntercroppingSubtotal = this.calculateFieldsSum(rowData, wheatIntercroppingFields)

      // 计算夏季作物小计
      const summerCropsFields = ['wheat', 'oilCrop', 'summerMisc']
      const summerCropsSum = this.calculateFieldsSum(rowData, summerCropsFields)
      rowData.summerCropsSubtotal =
        summerCropsSum + (rowData.melonVegetableSubtotal || 0) + (rowData.wheatIntercroppingSubtotal || 0)

      // 计算秋季作物小计
      const autumnCropsFields = [
        'autumnCorn',
        'gourd',
        'autumnPepper',
        'dehydratedVegetables',
        'autumnSunflower',
        'autumnMisc',
      ]
      rowData.autumnCropsSubtotal = this.calculateFieldsSum(rowData, autumnCropsFields)

      // 计算林地小计
      const forestlandFields = ['matureForest', 'youngForest', 'fruitTree', 'wolfberry']
      rowData.forestlandSubtotal = this.calculateFieldsSum(rowData, forestlandFields)

      // 计算林牧地小计
      const forestGrasslandFields = ['grassland']
      const forestGrasslandSum = this.calculateFieldsSum(rowData, forestGrasslandFields)
      rowData.forestGrasslandSubtotal = (rowData.forestlandSubtotal || 0) + forestGrasslandSum

      // 计算耕地面积（夏季作物小计 + 秋季作物小计）
      rowData.farmlandArea = (rowData.summerCropsSubtotal || 0) + (rowData.autumnCropsSubtotal || 0)

      // 计算总灌溉面积（耕地面积 + 林牧地小计）
      rowData.totalIrrigationArea = (rowData.farmlandArea || 0) + (rowData.forestGrasslandSubtotal || 0)
    },

    // 计算字段和
    calculateFieldsSum(rowData, fields) {
      let sum = 0
      fields.forEach(field => {
        sum += parseFloat(rowData[field]) || 0
      })
      return sum > 0 ? sum : null
    },

    // 获取渠道名称
    getChannelName(channelNo) {
      if (!channelNo) {
        return '未知渠道'
      }

      if (this.projectOptions.length === 0) {
        console.warn('项目数据尚未加载完成，显示渠道编号:', channelNo)
        return channelNo
      }

      const project = this.projectOptions.find(option => option.projectCode === channelNo)
      if (project) {
        return project.projectName
      } else {
        console.warn('未找到渠道名称，渠道编号:', channelNo)
        return channelNo
      }
    },

    // 根据灌溉类型获取类型名称
    getTypeNameByIrrigationType(irrigationType) {
      const typeMap = {
        1: '引黄灌溉',
        2: '引黄滴灌',
        3: '纯井灌',
      }
      return typeMap[irrigationType] || '未知类型'
    },

    // 获取行政区划名称
    async getDistrictName(districtCode) {
      // 先从缓存中查找
      const cachedDistrict = this.districtOptions.find(option => option.districtCode === districtCode)
      if (cachedDistrict) {
        return cachedDistrict.districtName
      }

      // 如果缓存中没有，调用接口获取
      try {
        const response = await getDistrictPage({
          districtCode: districtCode,
          parentId: 1,
          pageNum: 1,
          pageSize: 12,
          sort: [],
        })

        if (response.code === 200 && response.data && response.data.data && response.data.data.length > 0) {
          const district = response.data.data[0]

          // 缓存结果
          this.districtOptions.push({
            districtCode: district.districtCode,
            districtName: district.districtName,
            districtId: district.districtId,
          })

          return district.districtName
        }
      } catch (error) {
        console.error('获取行政区划名称失败:', error)
      }

      // 如果获取失败，返回原始编码
      return districtCode
    },

    // 设置表格标题
    setTableTitle(planYear) {
      // 获取当前选中的单位名称
      const currentDept = this.deptOptions.find(dept => dept.deptId === this.queryParam.depId)
      const unitName = currentDept ? currentDept.deptName : '永济灌域'

      // 根据不同的tab设置不同的标题
      let titleSuffix = ''

      if (this.queryParam.irrigationType === '4') {
        // 按行政区划分
        // titleSuffix = '（引黄+井灌种植意向）'
        titleSuffix = ''
      } else {
        // 按渠道分
        const tabTitleMap = {
          1: '（引黄灌溉种植意向）',
          2: '（引黄滴灌种植意向）',
          3: '（纯井灌种植意向）',
        }
        titleSuffix = tabTitleMap[this.queryParam.irrigationType] || '（种植意向）'
      }

      this.tableTitle = `${unitName}${planYear}年种植结构统计表${titleSuffix}`
    },

    // 初始化表格列配置
    initTableColumns() {
      const columns = []

      // 动态生成作物分类列（排除热水地和干地）
      this.plantingCropCategories.forEach((category, index) => {
        if (category.children) {
          // 有子分类的情况，创建分组列
          const groupColumn = {
            type: 'group',
            title: category.label,
            children: [],
          }

          category.children.forEach(subCategory => {
            if (subCategory.children && subCategory.children.length > 0) {
              // 有三级分类，创建子分组
              const subGroupColumn = {
                type: 'group',
                title: subCategory.label,
                children: [],
              }

              // 添加三级分类的列
              subCategory.children.forEach(crop => {
                if (crop.fieldName) {
                  subGroupColumn.children.push({
                    field: crop.fieldName,
                    title: crop.label,
                    width: 70,
                    cropInfo: crop,
                  })
                }
              })

              // 添加子分组小计列
              const subSubtotalField = this.getSubtotalFieldName(subCategory)
              if (subSubtotalField) {
                subGroupColumn.children.push({
                  field: subSubtotalField,
                  title: '小计',
                  width: 70,
                  isSubtotal: true,
                })
              }

              groupColumn.children.push(subGroupColumn)
            } else if (subCategory.fieldName) {
              // 没有三级分类，直接添加为列
              groupColumn.children.push({
                field: subCategory.fieldName,
                title: subCategory.label,
                width: 80,
                cropInfo: subCategory,
              })
            }
          })

          // 添加大分类小计列
          const subtotalField = this.getSubtotalFieldName(category)
          if (subtotalField) {
            groupColumn.children.push({
              field: subtotalField,
              title: '小计',
              width: 80,
              isSubtotal: true,
            })
          }

          columns.push(groupColumn)

          // 在秋季作物后添加耕地面积列
          if (category.value === 'AUTUMN_CROPS') {
            columns.push({
              field: 'farmlandArea',
              title: '耕地面积',
              width: 100,
            })
          }
        }
      })

      // 添加总灌溉面积列
      columns.push({
        field: 'totalIrrigationArea',
        title: '总灌溉面积',
        width: 120,
        isTotal: true,
      })

      // 添加热水地和干地列（放在最右侧）
      const hotWaterLand = plantingCrop.find(crop => crop.value === 'HOT_WATER_LAND')
      const dryLand = plantingCrop.find(crop => crop.value === 'DRY_LAND')

      if (hotWaterLand && hotWaterLand.fieldName) {
        columns.push({
          field: hotWaterLand.fieldName,
          title: hotWaterLand.label,
          width: 100,
          cropInfo: hotWaterLand,
        })
      }

      if (dryLand && dryLand.fieldName) {
        columns.push({
          field: dryLand.fieldName,
          title: dryLand.label,
          width: 100,
          cropInfo: dryLand,
        })
      }

      this.tableColumns = columns
    },

    // 初始化作物分类数据
    initPlantingCropCategories() {
      // 过滤出有子分类的作物（排除热水地和干地）
      this.plantingCropCategories = plantingCrop.filter(
        category => !['HOT_WATER_LAND', 'DRY_LAND'].includes(category.value),
      )
    },

    // 获取小计字段名
    getSubtotalFieldName(category) {
      const subtotalMap = {
        SUMMER_CROPS: 'summerCropsSubtotal',
        AUTUMN_CROPS: 'autumnCropsSubtotal',
        FOREST_PASTURE: 'forestGrasslandSubtotal',
        MELON_VEGETABLES: 'melonVegetableSubtotal',
        WHEAT_CATEGORY: 'wheatIntercroppingSubtotal',
        FOREST_LAND: 'forestlandSubtotal',
      }
      return subtotalMap[category.value] || null
    },

    // 格式化数字显示
    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '-'
      }
      const num = parseFloat(value)
      if (isNaN(num) || num === 0) {
        return '-'
      }
      return num.toFixed(2)
    },

    // 加载作物选项字典
    async loadCropOptions() {
      try {
        const response = await getOptions('crop')
        if (response.code === 200 && response.data) {
          this.cropOptions = response.data.map(item => ({
            key: item.key,
            value: item.value,
            label: item.value,
          }))
        }
      } catch (error) {
        console.error('获取作物选项失败:', error)
      }
    },

    // 加载项目（渠道）选项
    async loadProjectOptions() {
      try {
        const response = await getProjectPage({
          pageNum: 1,
          pageSize: 1000,
          districtCode: '0',
        })

        if (response.code === 200 && response.data && response.data.data) {
          this.projectOptions = response.data.data.map(item => ({
            projectId: item.projectId,
            projectCode: item.projectCode,
            projectName: item.projectName,
            projectNameAbbr: item.projectNameAbbr,
            objectCategoryName: item.objectCategoryName,
          }))
        }
      } catch (error) {
        console.error('获取项目数据失败:', error)
      }
    },

    handleImport() {
      this.importLoading = true
      // 模拟导入操作
      setTimeout(() => {
        this.importLoading = false
        this.$message.info('导入功能待实现')
      }, 1000)
    },

    handleExport() {
      this.exportLoading = true
      // 模拟导出操作
      setTimeout(() => {
        this.exportLoading = false
        this.$message.info('导出功能待实现')
      }, 1000)
    },
  },
}
</script>

<style lang="less" scoped>
.common-table-page {
  height: 100%;
  background: #fff;

  .flex,
  .ant-form-item {
    display: flex;
  }

  .row-pad {
    padding: 0 15px;
  }
}

.table-container {
  margin-top: 10px;
  padding: 0 15px;

  .table-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      flex: 1;
      text-align: center;
    }

    .unit-label {
      color: #666;
      font-size: 14px;
      font-weight: 500;
      white-space: nowrap;
    }
  }

  .plant-intention-table {
    /deep/ .vxe-header--column {
      font-weight: bold;
      background-color: #fafafa;
      font-size: 12px;
    }

    /deep/ .vxe-cell {
      padding: 4px 8px;
    }

    /deep/ .vxe-table--border-line {
      border-color: #e8e8e8;
    }

    /deep/ .vxe-body--row {
      &:nth-child(even) {
        background-color: #fafafa;
      }

      // 合计行背景色
      &:last-child {
        background-color: #fff7e6 !important;
        border-top: 2px solid #111 !important;
      }
    }

    /deep/ .vxe-table {
      width: 100% !important;
      font-size: 12px;
    }

    // 空数据状态样式
    /deep/ .vxe-table--empty-block {
      padding: 40px 0;
      color: #999;
      font-size: 14px;
    }

    // 滚动条样式优化
    /deep/ .vxe-table--body-wrapper {
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;

        &:hover {
          background: #a8a8a8;
        }
      }
    }

    // 固定表头样式
    /deep/ .vxe-table--header-wrapper {
      background-color: #fafafa;
      border-bottom: 2px solid #e8e8e8;
    }

    // 类型列样式
    .type-cell {
      font-weight: 600;
      color: #111111;
      text-align: center;
      display: block;
    }

    // 合计行样式
    .total-cell {
      font-weight: bold;
      color: #111;
      // text-align: center;
      display: block;
    }
  }
}

.table-operations {
  .ant-btn {
    margin-left: 8px;

    &:first-child {
      margin-left: 0px;
    }
  }
}

::v-deep(.header-bar) {
  .title {
    text-align: center;
    width: 100%;
    position: absolute;
  }
}
</style>
